<template>
  <div class="message-panel">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="header-info">
        <el-avatar :size="30" :src="conversation.avatar">
          <el-icon v-if="conversation.type === 'group'"><ChatLineRound /></el-icon>
          <el-icon v-else><User /></el-icon>
        </el-avatar>
        <div class="info-text">
          <div class="name">{{ conversation.name }}</div>
          <!-- <div class="status" v-if="conversation.type === 'friend'">
            <span :class="conversation.online ? 'online' : 'offline'">
              {{ conversation.online ? '在线' : '离线' }}
            </span>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="message-list" ref="messageListRef">
      <el-scrollbar ref="scrollbarRef" class="message-scrollbar" @scroll="handleScroll">
        <div class="messages-container">
          <!-- 加载更多指示器 -->
          <div v-if="isLoadingMore" class="loading-more">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载更多消息...</span>
          </div>

          <!-- 没有更多消息提示 -->
          <div v-else-if="!hasMoreMessages && messages.length > 0" class="no-more-messages">
            没有更多消息了
          </div>

          <div class="message-content">
            <div v-for="(messageGroup, date) in groupedMessages" :key="date" class="message-group">
              <div class="date-divider">{{ date }}</div>
              <div v-for="message in messageGroup" :key="message.id" class="message-item">
                <MessageItem
                  :message="message"
                  :is-own="message.isOwn"
                  @avatar-click="handleAvatarClick"
                />
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <!-- 消息输入框 -->
    <div class="message-input">
      <MessageEditor @send="handleSendMessage" @send-image="handleSendImageMessage" @send-voice="handleSendVoiceMessage" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue'
import MessageItem from './MessageItem.vue'
import MessageEditor from './MessageEditor.vue'
import { sendMessage as sendMessageToServer } from '@/utils/chatService.js'
import { addTabItem } from '@/utils/db.js'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import { decryptAESBase64 } from '@/utils/decrypt.js'
import { useUserStore } from '@/pinia/modules/user.js'

defineOptions({
  name: 'MessagePanel'
})

const props = defineProps({
  conversation: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['start-chat'])

const messageListRef = ref()
const scrollbarRef = ref()
const userStore = useUserStore()

// 获取当前用户ID
const getCurrentUserId = () => {
  // 首先尝试从用户store获取
  if (userStore.userInfo && (userStore.userInfo.ID || userStore.userInfo.uuid || userStore.userInfo.id)) {
    return userStore.userInfo.ID || userStore.userInfo.uuid || userStore.userInfo.id
  }

  // 然后从localStorage/sessionStorage获取
  const storedUserId = localStorage.getItem('userId') || sessionStorage.getItem('userId')
  if (storedUserId) {
    return storedUserId
  }

  // 如果都没有，返回null
  console.warn('无法获取当前用户ID，用户可能未登录')
  return null
}

const currentUserId = computed(() => getCurrentUserId())

// 消息数据
const messages = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const hasMoreMessages = ref(true)
const isLoadingMore = ref(false)

// 加载聊天消息（初始加载）
const loadMessages = async (reset = true) => {
  try {
    if (!props.conversation) {
      console.log('没有选中的会话，跳过加载消息')
      return
    }

    const chatId = props.conversation.type === 'group'
      ? props.conversation.originalData?.ID?.toString()
      : props.conversation.id.replace('friend_', '')

    if (!chatId) {
      console.log('无法获取chatId，跳过加载消息')
      return
    }

    console.log('开始加载聊天消息，chatId:', chatId, 'chatId类型:', typeof chatId, '会话信息:', props.conversation)
    console.log('会话详细信息:', {
      type: props.conversation.type,
      id: props.conversation.id,
      originalData: props.conversation.originalData
    })

    if (reset) {
      // 重置分页状态
      currentPage.value = 1
      hasMoreMessages.value = true
      messages.value = []
    }

    // 确保数据库已初始化
    const { openDb, getChatMessages, debugAllMessages } = await import('@/utils/db.js')
    await openDb()

    // 调试：查看数据库中的所有数据
    if (currentPage.value === 1) {
      console.log('=== 调试数据库内容 ===')
      await debugAllMessages()
    }

    // 从数据库加载消息
    const dbMessages = await getChatMessages(chatId, currentPage.value, pageSize.value)
    console.log('从数据库加载的消息:', dbMessages)

    if (!dbMessages || dbMessages.length === 0) {
      console.log('数据库中没有找到消息')
      if (reset) {
        messages.value = []
      }
      hasMoreMessages.value = false
      return
    }

    // 确保消息有正确的头像信息
    const { processAvatarUrl, getDefaultAvatar } = await import('@/utils/avatarService.js')
    const processedMessages = dbMessages.map(msg => ({
      ...msg,
      avatar: processAvatarUrl(msg.avatar) || getDefaultAvatar(),
      senderAvatar: processAvatarUrl(msg.senderAvatar) || getDefaultAvatar()
    }))

    // 转换消息格式
    const convertedMessages = processedMessages.map(msg => {
      const fromidStr = msg.fromid?.toString()
      const currentUserIdStr = currentUserId.value?.toString()
      const isOwn = fromidStr === currentUserIdStr

      return {
        id: msg.id,
        userId: fromidStr,
        nickname: isOwn ? '我' : (msg.senderNickname || msg.nickname || `用户${msg.fromid}`),
        avatar: msg.avatar, // 使用处理过的头像URL
        senderAvatar: msg.senderAvatar, // 使用处理过的头像URL
        content: msg.msg,
        type: getMessageType(msg.typecode2),
        createdAt: msg.timestamp ? new Date(msg.timestamp) : new Date(msg.t),
        isOwn: isOwn,
        status: msg.isRedRead ? 'read' : 'sent'
      }
    })

    if (reset) {
      messages.value = convertedMessages
    } else {
      // 分页加载时，将历史消息添加到顶部
      messages.value = [...convertedMessages, ...messages.value]
    }

    // 检查是否还有更多消息
    if (dbMessages.length < pageSize.value) {
      hasMoreMessages.value = false
    }

    console.log('转换后的消息数量:', messages.value.length)

    // 延迟滚动到底部（仅初始加载时）
    if (reset) {
      nextTick(() => {
        scrollToBottom()
      })
    }

  } catch (error) {
    console.error('加载聊天消息失败:', error)
    if (reset) {
      messages.value = []
    }
  }
}

// 加载更多消息（分页）
const loadMoreMessages = async () => {
  if (isLoadingMore.value || !hasMoreMessages.value) {
    return
  }

  try {
    isLoadingMore.value = true
    currentPage.value++

    // 记录当前滚动位置
    const messagesContainer = document.querySelector('.messages-container')
    const scrollHeight = messagesContainer?.scrollHeight || 0

    await loadMessages(false)

    // 恢复滚动位置（保持在加载前的位置）
    if (messagesContainer) {
      nextTick(() => {
        const newScrollHeight = messagesContainer.scrollHeight
        const scrollDiff = newScrollHeight - scrollHeight
        messagesContainer.scrollTop = scrollDiff
      })
    }

  } catch (error) {
    console.error('加载更多消息失败:', error)
    currentPage.value-- // 回退页码
  } finally {
    isLoadingMore.value = false
  }
}

// 根据typecode2获取消息类型
const getMessageType = (typecode2) => {
  switch (typecode2) {
    case 0: return 'text'
    case 1: return 'audio'
    case 2: return 'image'
    case 3: return 'voice'  // 语音消息
    case 4: return 'video'
    case 5: return 'forward'
    case 6: return 'retract'
    default: return 'text'
  }
}

// 按日期分组消息
const groupedMessages = computed(() => {
  const groups = {}
  messages.value.forEach(message => {
    const date = formatDate(message.createdAt)
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(message)
  })
  return groups
})

// 格式化日期
const formatDate = (date) => {
  const now = new Date()
  const messageDate = new Date(date)
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const messageDay = new Date(messageDate.getFullYear(), messageDate.getMonth(), messageDate.getDate())

  if (messageDay.getTime() === today.getTime()) {
    return '今天'
  } else if (messageDay.getTime() === yesterday.getTime()) {
    return '昨天'
  } else {
    return messageDate.toLocaleDateString('zh-CN', { 
      month: 'long', 
      day: 'numeric' 
    })
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (scrollbarRef.value) {
      const scrollbar = scrollbarRef.value
      const wrapRef = scrollbar.wrapRef
      wrapRef.scrollTop = wrapRef.scrollHeight
    }
  })
}



// 处理滚动事件
const handleScroll = (scrollInfo) => {
  // 当滚动到顶部附近时，加载更多消息
  if (scrollInfo.scrollTop < 100 && hasMoreMessages.value && !isLoadingMore.value) {
    loadMoreMessages()
  }
}

// 发送消息的通用函数
const sendMessage = async (content, messageType = 0) => {
  try {
    if (!props.conversation || !content.trim()) return

    const isGroup = props.conversation.type === 'group'
    const targetId = isGroup
      ? props.conversation.originalData?.ID
      : parseInt(props.conversation.id.replace('friend_', ''))

    if (!targetId) {
      ElMessage.error('无法获取目标ID')
      return
    }

    // 检查用户ID
    if (!currentUserId.value) {
      ElMessage.error('用户未登录，无法发送消息')
      return
    }

    // 构建消息参数
    const messageParams = {
      fromid: parseInt(currentUserId.value),
      toId: targetId,
      msg: content.trim(),
      typecode: isGroup ? 2 : 1, // 1-好友消息，2-群组消息
      typecode2: messageType, // 0-文本消息，2-图片消息，3-语音消息
      ...(isGroup && { groupID: targetId })
    }

    console.log('发送消息参数:', messageParams)

    // 发送消息到服务器
    const result = await sendMessageToServer(messageParams)
    console.log('消息发送结果:', result)

    // 确定消息类型
    const messageTypeMap = {
      0: 'text',
      2: 'image',
      3: 'voice'
    }

    // 立即添加到本地消息列表（乐观更新）
    const newMessage = {
      id: Date.now(),
      userId: currentUserId.value,
      nickname: '我',
      avatar: '',
      content: content.trim(),
      type: messageTypeMap[messageType] || 'text',
      createdAt: new Date(),
      isOwn: true
    }

    messages.value.push(newMessage)
    scrollToBottom()

    // 获取当前用户头像信息
    const { getCurrentUserAvatar } = await import('@/utils/avatarService.js')
    const currentUserInfo = getCurrentUserAvatar()

    // 保存到本地数据库
    const messageItem = {
      id: Date.now(),
      typecode: messageParams.typecode,
      typecode2: messageParams.typecode2,
      toid: messageParams.toId,
      fromid: messageParams.fromid,
      chatid: targetId.toString(),
      t: new Date().toISOString(),
      msg: content.trim(),
      isRedRead: 1, // 自己发送的消息标记为已读
      idDel: 0,
      senderAvatar: currentUserInfo.avatar,
      senderNickname: currentUserInfo.nickname,
      avatar: currentUserInfo.avatar,
      nickname: isGroup ? props.conversation.name : currentUserInfo.nickname,
      lastMessage: content.trim(),
      timestamp: new Date().getTime(),
      unreadCount: 0
    }

    await addTabItem(messageItem)
    console.log('消息已保存到本地数据库')

    // 发送消息后立即触发群聊列表更新事件
    try {
      const isGroup = props.conversation?.type === 'group'
      if (isGroup) {
        const groupId = props.conversation.originalData?.ID
        if (groupId) {
          const updateEvent = new CustomEvent('groupMessageUpdate', {
            detail: {
              groupId: groupId,
              messageData: {
                msg: content.trim(),
                t: new Date().toISOString(),
                typecode2: messageType, // 消息类型
                senderNickname: '我' // 自己发送的消息
              }
            }
          })
          window.dispatchEvent(updateEvent)
          console.log('发送消息后已发射群聊更新事件:', updateEvent.detail)
        }
      }
    } catch (error) {
      console.warn('发射发送消息更新事件失败:', error)
    }

  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败')
  }
}

// 发送文本消息
const handleSendMessage = async (content) => {
  await sendMessage(content, 0) // 0 表示文本消息
}

// 发送图片消息
const handleSendImageMessage = async (imageUrl) => {
  await sendMessage(imageUrl, 2) // 2 表示图片消息
}

// 发送语音消息
const handleSendVoiceMessage = async (voiceData) => {
  // voiceData 包含 { url, duration }
  const voiceMessage = JSON.stringify({
    url: voiceData.url,
    duration: voiceData.duration
  })
  await sendMessage(voiceMessage, 3) // 3 表示语音消息
}

// 头像点击
const handleAvatarClick = (message) => {
  console.log('点击头像:', message)

  // 如果是自己的消息，不处理
  if (message.isOwn) {
    return
  }

  // 构建私聊数据，格式与 GroupUserPanel 中的 startPrivateChat 一致
  const privateChatData = {
    id: `friend_${message.fromid || message.senderId}`,
    type: 'friend',
    name: message.nickname || message.senderNickname || `用户${message.fromid || message.senderId}`,
    avatar: message.avatar || message.senderAvatar || '',
    lastMessage: '开始聊天',
    lastTime: new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }),
    unread: 0,
    online: true,
    originalData: {
      ID: message.fromid || message.senderId,
      userId: message.fromid || message.senderId,
      nickname: message.nickname || message.senderNickname,
      avatar: message.avatar || message.senderAvatar,
      phone: message.phone || ''
    }
  }

  // 触发开始私聊事件，传递给父组件处理
  emit('start-chat', privateChatData)
}

// WebSocket消息监听器
let messageListener = null

// 设置消息监听
const setupMessageListener = () => {
  messageListener = async (data) => {
    console.log('收到WebSocket消息:', data)

    // 检查是否是当前会话的消息
    if (!props.conversation) return

    const isGroup = props.conversation.type === 'group'
    const currentChatId = isGroup
      ? props.conversation.originalData?.ID?.toString()
      : props.conversation.id.replace('friend_', '')

    const messageChatId = isGroup
      ? data.groupID?.toString() || data.toid?.toString()
      : data.fromid?.toString()

    if (currentChatId === messageChatId) {
      // 添加接收到的消息到列表
      const fromidStr = data.fromid?.toString()
      const currentUserIdStr = currentUserId.value?.toString()
      const isOwn = fromidStr === currentUserIdStr

      // 解密消息内容
      let decryptedMsg = data.msg
      if (data.msg && typeof data.msg === 'string' && !isOwn) {
        try {
          decryptedMsg = decryptAESBase64(data.msg)
          console.log('消息解密成功:', {
            original: data.msg,
            decrypted: decryptedMsg
          })
        } catch (decryptError) {
          console.warn('消息解密失败，使用原始消息:', decryptError)
          decryptedMsg = data.msg
        }
      }

      console.log(`接收消息详情:`, {
        id: data.id,
        fromid: data.fromid,
        fromidType: typeof data.fromid,
        fromidStr: fromidStr,
        currentUserId: currentUserId.value,
        currentUserIdType: typeof currentUserId.value,
        currentUserIdStr: currentUserIdStr,
        isOwn: isOwn,
        originalMsg: data.msg,
        decryptedMsg: decryptedMsg
      })

      // 获取发送者信息
      let senderInfo = {
        avatar: '',
        nickname: isOwn ? '我' : `用户${data.fromid}`
      }

      if (!isOwn) {
        try {
          if (isGroup) {
            // 群聊消息：从群成员信息中获取
            const { getUserInfoFromGroupMembers } = await import('@/utils/avatarService.js')
            senderInfo = await getUserInfoFromGroupMembers(data.fromid, data.groupID)
          } else {
            // 私聊消息：从用户信息中获取
            const { getUserInfo } = await import('@/utils/avatarService.js')
            senderInfo = await getUserInfo(data.fromid)
          }
        } catch (error) {
          console.warn('获取发送者信息失败:', error)
        }
      } else {
        // 自己发送的消息，获取当前用户信息
        const { getCurrentUserAvatar } = await import('@/utils/avatarService.js')
        senderInfo = getCurrentUserAvatar()
      }

      // 构建数据库存储对象
      const messageItem = {
        id: data.id || Date.now().toString(),
        typecode: data.typecode || (isGroup ? 2 : 1), // 1-私聊，2-群聊
        typecode2: data.typecode2 || 0, // 内容类型
        toid: data.toid || 0,
        fromid: data.fromid || 0,
        chatid: isGroup ? (data.groupID || data.toid) : data.fromid,
        t: data.t || new Date().toISOString(),
        msg: decryptedMsg, // 存储解密后的消息
        isRedRead: isOwn ? 1 : 0, // 自己发的消息标记为已读，别人发的标记为未读
        idDel: 0,
        avatar: senderInfo.avatar || '',
        nickname: senderInfo.nickname || (isOwn ? '我' : `用户${data.fromid}`),
        senderAvatar: senderInfo.avatar || '',
        senderNickname: senderInfo.nickname || (isOwn ? '我' : `用户${data.fromid}`),
        lastMessage: decryptedMsg,
        timestamp: new Date(data.t || new Date()).getTime(),
        unreadCount: isOwn ? 0 : 1
      }

      try {
        // 保存到IndexedDB
        await addTabItem(messageItem)
        console.log('接收到的消息已保存到数据库:', messageItem)

        // 接收消息后立即触发列表更新事件
        try {
          if (isGroup) {
            // 群聊消息更新事件
            const groupId = data.groupID || data.toid
            if (groupId) {
              const updateEvent = new CustomEvent('groupMessageUpdate', {
                detail: {
                  groupId: groupId,
                  messageData: {
                    msg: decryptedMsg,
                    t: data.t || new Date().toISOString(),
                    typecode2: data.typecode2 || 0,
                    senderNickname: senderInfo.nickname || `用户${data.fromid}`
                  }
                }
              })
              window.dispatchEvent(updateEvent)
              console.log('接收消息后已发射群聊更新事件:', updateEvent.detail)
            }
          } else {
            // 好友私聊消息更新事件
            const userId = data.fromid
            if (userId) {
              const updateEvent = new CustomEvent('privateMessageUpdate', {
                detail: {
                  userId: userId,
                  messageData: {
                    msg: decryptedMsg,
                    t: data.t || new Date().toISOString(),
                    typecode2: data.typecode2 || 0,
                    senderNickname: senderInfo.nickname || `用户${data.fromid}`,
                    content: decryptedMsg,
                    timestamp: new Date().toISOString(),
                    type: data.typecode2 || 0
                  }
                }
              })
              window.dispatchEvent(updateEvent)
              console.log('接收消息后已发射好友更新事件:', updateEvent.detail)
            }
          }
        } catch (error) {
          console.warn('发射接收消息更新事件失败:', error)
        }

      } catch (error) {
        console.error('保存接收消息到数据库失败:', error)
      }

      // 构建界面显示对象
      const receivedMessage = {
        id: data.id || Date.now(),
        userId: fromidStr,
        nickname: senderInfo.nickname || (isOwn ? '我' : `用户${data.fromid}`),
        avatar: senderInfo.avatar || '',
        senderAvatar: senderInfo.avatar || '',
        content: decryptedMsg, // 显示解密后的消息
        type: getMessageType(data.typecode2),
        createdAt: new Date(data.t || new Date()),
        isOwn: isOwn,
        status: 'received'
      }

      // 避免重复添加自己发送的消息
      if (!receivedMessage.isOwn) {
        messages.value.push(receivedMessage)
        scrollToBottom()
      }
    }
  }

  // 直接从WebSocket管理器添加消息处理器
  import('@/utils/websocket.js').then(({ default: webSocketManager }) => {
    // 监听所有消息类型
    webSocketManager.addMessageHandler('all', messageListener)
    console.log('MessagePanel已添加WebSocket消息监听器')
  })
}

// 移除消息监听
const removeMessageListener = () => {
  if (messageListener) {
    import('@/utils/websocket.js').then(({ default: webSocketManager }) => {
      webSocketManager.removeMessageHandler('all')
      messageListener = null
      console.log('MessagePanel已移除WebSocket消息监听器')
    })
  }
}

// 组件挂载时设置监听器
onMounted(() => {
  console.log('MessagePanel组件挂载，当前会话:', props.conversation)
  setupMessageListener()
  if (props.conversation) {
    loadMessages(true)
  }
})

// 组件卸载时移除监听器
onUnmounted(() => {
  removeMessageListener()
})

// 监听会话变化，重新加载消息
watch(() => props.conversation, (newConversation) => {
  if (newConversation) {
    console.log('会话切换，重新加载消息:', newConversation)
    // 重置状态并加载消息
    loadMessages(true)
  }
}, { immediate: true })

// 监听消息变化，滚动到底部（仅在非分页加载时）
watch(messages, (newMessages, oldMessages) => {
  // 如果是新增消息（不是分页加载），则滚动到底部
  if (newMessages.length > oldMessages.length && !isLoadingMore.value) {
    scrollToBottom()
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.message-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #374151;
}

.chat-header {
  padding: 13px 20px;
  background: #4b5563;
  border-bottom: 1px solid #6b7280;

  .header-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .info-text {
      .name {
        font-size: 16px;
        font-weight: 500;
        color: #f3f4f6;
        margin-bottom: 2px;
      }

      .status {
        font-size: 12px;

        .online {
          color: #22c55e;
        }

        .offline {
          color: #9ca3af;
        }
      }
    }
  }
}

.message-list {
  flex: 1;
  overflow: hidden;
  // height: calc(750px - 120px - 80px); // 总高度 - 头部 - 输入框
  // min-height: calc(750px - 120px - 80px);
  // max-height: calc(750px - 120px - 80px);

  .message-scrollbar {
    height: 100%;
  }

  .message-content {
    padding: 20px;
  }

  .message-group {
    margin-bottom: 20px;

    .date-divider {
      text-align: center;
      color: #9ca3af;
      font-size: 12px;
      margin-bottom: 15px;
      position: relative;

      /* &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 30%;
        height: 1px;
        background: #6b7280;
      }
 */
      &::before {
        left: 0;
      }

      &::after {
        right: 0;
      }
    }

    .message-item {
      margin-bottom: 15px;
    }
  }

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    color: #9ca3af;
    font-size: 14px;
    gap: 8px;

    .el-icon {
      font-size: 16px;
    }
  }

  .no-more-messages {
    text-align: center;
    padding: 15px;
    color: #6b7280;
    font-size: 12px;
  }
}

.message-input {
  background: #4b5563;
  border-top: 1px solid #6b7280;
}
</style>
